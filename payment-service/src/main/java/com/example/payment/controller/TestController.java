package com.example.payment.controller;

import com.example.common.result.Result;
import com.example.payment.factory.impl.AliPayImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Create by 2025/7/30 15:31
 * desc
 */
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TestController {
    private final AliPayImpl aliPay;

    @GetMapping("/test1")
    public Result test1(){
        return Result.success(aliPay.payStrategyCode());
    }
}
