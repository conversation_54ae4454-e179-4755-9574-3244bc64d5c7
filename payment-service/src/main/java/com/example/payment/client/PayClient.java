package com.example.payment.client;

import com.example.payment.factory.StrategyFactory;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * Create by 2025/7/31 15:29
 * desc 支付策略模式客户端
 */
@Data
public class PayClient {
    private StrategyFactory strategyFactory;

    public PayClient(StrategyFactory strategyFactory) {
        this.strategyFactory = strategyFactory;
    }

    public String pay() {
        if(Objects.isNull(strategyFactory)){
            throw new RuntimeException("暂无其他支付方式");
        }
        return strategyFactory.payStrategy();
    }
}
