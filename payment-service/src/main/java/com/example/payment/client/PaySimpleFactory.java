package com.example.payment.client;

import com.example.payment.factory.impl.AliPayImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Create by 2025/7/31 15:32
 * desc 支付简单工厂模式
 */
@RequiredArgsConstructor
@Component
public class PaySimpleFactory {
    private final AliPayImpl aliPay;

    public String pay(String payType) {
        if("aliPay".equals(payType)){
            return new PayClient(aliPay).pay();
        }
        throw new RuntimeException("暂无其他支付方式");
    }
}
