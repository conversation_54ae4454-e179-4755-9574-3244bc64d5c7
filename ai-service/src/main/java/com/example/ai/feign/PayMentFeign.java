package com.example.ai.feign;

import com.example.ai.dto.ChatResponse;
import com.example.common.entity.dto.AiChatSessionDto;
import com.example.common.entity.vo.AiChatSessionVo;
import com.example.common.entity.vo.PaymentMethodVo;
import com.example.common.entity.vo.VipPlanVo;
import com.example.common.result.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * payment服务Feign客户端
 * 用于调用payment-service的相关接口
 * 配置了FallbackFactory以支持降级功能
 * 修复版本 - 使用FallbackFactory确保降级功能正确工作
 */
@FeignClient(
    value = "payment-service",
    fallbackFactory = BusinessFeignFallback.class
)
public interface PayMentFeign {


    @GetMapping("/vip-plan/getAllVipPlansFeatures")
    Result<List<VipPlanVo>> getAllVipPlansFeatures();

    @GetMapping("/payment-method/paymentMethodList")
    Result<List<PaymentMethodVo>> paymentMethodList();

    @GetMapping("/recharge/getList")
    Result getRechargeList();


    @GetMapping("/test/test1")
    Result getAliPayCode();


    @GetMapping("/order/getOrderNum")
    Result getOrderNum();


    /**
     * 根据支付类型 获取预支付下单页面 以及唯一订单号
     *
     * @param payType
     * @return
     */
    @PostMapping("/order/getPagePayCode")
    Result getPagePayCode(String payType);

}

