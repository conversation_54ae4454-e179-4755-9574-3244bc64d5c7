package com.example.ai.service.impl;

import com.example.ai.feign.PayMentFeign;
import com.example.ai.service.PayMentService;
import com.example.common.entity.vo.PaymentMethodVo;
import com.example.common.entity.vo.VipPlanVo;
import com.example.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * Create by 2025/7/23 17:13
 * desc
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayMentServiceImpl implements PayMentService {

    private final PayMentFeign payMentFeign;


    @Override
    public List<VipPlanVo> getVipPlans() {
        try {
            Result<List<VipPlanVo>> allVipPlansFeatures = payMentFeign.getAllVipPlansFeatures();
            if(allVipPlansFeatures.getCode()==200){
                log.info("远程调用成功");
                return allVipPlansFeatures.getData();
            }
        } catch (Exception e) {
            log.error("远程调用失败{}",e.getMessage());
            throw new RuntimeException(e);
        }
        return List.of();
    }

    @Override
    public List<PaymentMethodVo> paymentMethodList() {
        try {
            Result<List<PaymentMethodVo>> allVipPlansFeatures = payMentFeign.paymentMethodList();
            if(allVipPlansFeatures.getCode()==200){
                log.info("远程调用成功");
                return allVipPlansFeatures.getData();
            }
        } catch (Exception e) {
            log.error("远程调用失败{}",e.getMessage());
            throw new RuntimeException(e);
        }
        return List.of();
    }

    @Override
    public Result getRechargeList() {
        try {
            Result list = payMentFeign.getRechargeList();
            if(list.getCode()==200){
                return Result.success(list.getData());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.error("服务异常，请稍后重试");
    }

    @Override
    public Result getAliPayCode() {
        try {
            Result list = payMentFeign.getAliPayCode();
            if(list.getCode()==200){
                return Result.success(list.getData());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.error("服务异常，请稍后重试");
    }

    @Override
    public Result getOrderNum() {
        try {
            Result orderNum = payMentFeign.getOrderNum();
            if(orderNum.getCode()==200){
                return Result.success(orderNum.getData());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.error("服务异常，请稍后重试");
    }

    @Override
    public Result getPagePayCode(String payType) {
        try {
            Result result = payMentFeign.getPagePayCode(payType);
            if(result.getCode()==200){
                return Result.success(result.getData());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.error("服务异常，请稍后重试");
    }
}
